# 时间处理优化功能说明

## 🎯 功能概述

本次优化针对网络时间获取失败导致的本地时间异常问题，实现了三个核心改进：

1. **多时间服务器支持** - 提高时间获取的可靠性
2. **智能重试机制** - 增强网络异常情况下的容错能力  
3. **时间偏差容错** - 允许合理范围内的时间偏差

## 🌐 1. 多时间服务器支持

### 默认时间服务器配置
```typescript
private readonly timeServers: TimeServerConfig[] = [
  {
    url: "https://b2m.sieyuan.com.cn/",
    timeout: 5000,
    description: "主时间服务器"
  },
  {
    url: "https://www.baidu.com/",
    timeout: 5000,
    description: "百度服务器"
  },
  {
    url: "https://www.qq.com/",
    timeout: 5000,
    description: "腾讯服务器"
  },
  {
    url: "https://www.taobao.com/",
    timeout: 5000,
    description: "淘宝服务器"
  }
];
```

### 工作机制
- 按顺序尝试每个时间服务器
- 第一个成功的服务器响应将被使用
- 所有服务器失败后降级使用本地时间

### API 方法
```typescript
// 添加自定义时间服务器
myAuthMgr.addTimeServer('https://www.google.com/', 3000, 'Google服务器');

// 获取当前服务器列表
const servers = myAuthMgr.getTimeServers();
```

## 🔄 2. 智能重试机制

### 重试配置
```typescript
private readonly maxRetries = 2;           // 每个服务器最多重试2次
private readonly retryDelayBase = 1000;    // 基础延迟1秒
```

### 指数退避策略
- 第1次重试：延迟 1秒
- 第2次重试：延迟 2秒
- 第3次重试：延迟 4秒

### 重试日志示例
```
尝试从 主时间服务器 获取时间 (重试 0/2)
❌ 主时间服务器 获取时间失败 (重试 0/2): Network timeout
⏳ 等待 1000ms 后重试...
尝试从 主时间服务器 获取时间 (重试 1/2)
```

## ⏰ 3. 时间偏差容错

### 默认容错设置
```typescript
private readonly timeToleranceSeconds = 300; // 允许5分钟的时间偏差
```

### 容错机制
- 当前时间早于授权开始时间时触发
- 在容错范围内：自动调整并继续验证
- 超出容错范围：报告时间异常

### 容错日志示例
```
⚠️ 时间偏差在容错范围内: 180秒 (允许300秒)
✅ 应用时间容错，继续授权验证
```

### API 方法
```typescript
// 设置时间容错（秒）
myAuthMgr.setTimeTolerance(600); // 10分钟

// 获取当前容错设置
const tolerance = myAuthMgr.getTimeTolerance();
```

## 📊 增强的时间信息

### TimeResult 接口
```typescript
interface TimeResult {
  timestamp: number;      // 时间戳
  isNetworkTime: boolean; // 是否为网络时间
  localTime: number;      // 本地时间
  serverUsed?: string;    // 使用的服务器
  retryCount?: number;    // 重试次数
}
```

### 详细的错误报告
```typescript
console.error("❌ 时间异常检测:", {
  currentTime: new Date(curTs * 1000).toLocaleString(),
  authStartTime: new Date(data.startTs * 1000).toLocaleString(),
  isNetworkTime: timeInfo.isNetworkTime,
  timeDiff: timeDiffFromStart,
  timeDiffAbs: timeDiffAbs,
  toleranceSeconds: this.timeToleranceSeconds,
  serverUsed: timeInfo.serverUsed || "本地时间",
  retryCount: timeInfo.retryCount || 0
});
```

## 🚀 使用示例

### 基本使用
```javascript
const { myAuthMgr } = require('license');

// 初始化
await myAuthMgr.init();

// 设置10分钟时间容错
myAuthMgr.setTimeTolerance(600);

// 添加自定义时间服务器
myAuthMgr.addTimeServer('https://api.example.com/time', 3000, '自定义API');

// 进行授权检查（会自动使用新的时间获取机制）
const result = await myAuthMgr.checkAuth('YourSoftware');
```

### 配置查看
```javascript
// 查看当前配置
console.log('时间容错:', myAuthMgr.getTimeTolerance(), '秒');
console.log('时间服务器:', myAuthMgr.getTimeServers());
```

## 🔧 配置建议

### 时间容错设置
- **严格模式**: 0-60秒 (适用于高安全要求)
- **标准模式**: 300秒 (5分钟，默认设置)
- **宽松模式**: 600-1800秒 (10-30分钟，适用于网络不稳定环境)

### 时间服务器选择
- 优先使用地理位置较近的服务器
- 选择稳定性高的知名服务
- 建议配置3-5个备选服务器

## 📈 性能优化

### 缓存机制（未来版本）
- 缓存最近获取的网络时间
- 减少频繁的网络请求
- 智能缓存失效策略

### 并发获取（未来版本）
- 同时请求多个时间服务器
- 使用最快响应的结果
- 提高时间获取速度

## 🐛 故障排除

### 常见问题
1. **所有时间服务器都无法访问**
   - 检查网络连接
   - 检查防火墙设置
   - 考虑添加内网时间服务器

2. **时间偏差过大**
   - 检查系统时间设置
   - 调整时间容错范围
   - 验证授权数据的时间戳

3. **重试次数过多**
   - 检查网络稳定性
   - 调整超时时间设置
   - 考虑减少服务器数量

### 调试日志
系统会输出详细的调试信息，包括：
- 时间服务器尝试过程
- 重试和延迟信息
- 时间偏差检测结果
- 容错机制应用情况
