/**
 * 时间处理优化功能使用示例
 */

const { myAuthMgr } = require('./license');

async function basicExample() {
  console.log('=== 基本使用示例 ===\n');
  
  try {
    // 1. 初始化授权管理器
    await myAuthMgr.init();
    console.log('✅ 授权管理器初始化完成');
    
    // 2. 查看默认配置
    console.log('\n📋 默认配置:');
    console.log('  时间容错:', myAuthMgr.getTimeTolerance(), '秒');
    console.log('  时间服务器数量:', myAuthMgr.getTimeServers().length);
    
    // 3. 自定义配置
    console.log('\n⚙️ 自定义配置:');
    
    // 设置时间容错为10分钟
    myAuthMgr.setTimeTolerance(600);
    
    // 添加自定义时间服务器
    myAuthMgr.addTimeServer('https://www.google.com/', 3000, 'Google服务器');
    
    console.log('  更新后时间容错:', myAuthMgr.getTimeTolerance(), '秒');
    console.log('  更新后服务器数量:', myAuthMgr.getTimeServers().length);
    
    // 4. 进行授权检查
    console.log('\n🔐 进行授权检查:');
    const result = await myAuthMgr.checkAuth('TestSoftware');
    
    console.log('  错误码:', result.errCode);
    console.log('  错误信息:', result.errInfo);
    
  } catch (error) {
    console.error('❌ 示例执行失败:', error);
  }
}

async function advancedExample() {
  console.log('\n=== 高级配置示例 ===\n');
  
  try {
    await myAuthMgr.init();
    
    // 1. 为不同环境配置不同的容错策略
    const environments = {
      'production': 300,    // 生产环境：5分钟容错
      'testing': 600,       // 测试环境：10分钟容错
      'development': 1800   // 开发环境：30分钟容错
    };
    
    const currentEnv = process.env.NODE_ENV || 'development';
    const tolerance = environments[currentEnv] || 300;
    
    console.log(`🌍 当前环境: ${currentEnv}`);
    console.log(`⏰ 设置时间容错: ${tolerance}秒 (${Math.floor(tolerance/60)}分钟)`);
    myAuthMgr.setTimeTolerance(tolerance);
    
    // 2. 添加多个备选时间服务器
    const customServers = [
      { url: 'https://www.microsoft.com/', timeout: 3000, desc: 'Microsoft服务器' },
      { url: 'https://www.apple.com/', timeout: 3000, desc: 'Apple服务器' },
      { url: 'https://www.github.com/', timeout: 3000, desc: 'GitHub服务器' }
    ];
    
    console.log('\n🌐 添加自定义时间服务器:');
    customServers.forEach(server => {
      myAuthMgr.addTimeServer(server.url, server.timeout, server.desc);
    });
    
    // 3. 显示最终配置
    console.log('\n📊 最终配置:');
    console.log('  时间容错:', myAuthMgr.getTimeTolerance(), '秒');
    
    const servers = myAuthMgr.getTimeServers();
    console.log('  时间服务器列表:');
    servers.forEach((server, index) => {
      console.log(`    ${index + 1}. ${server.description}: ${server.url}`);
    });
    
  } catch (error) {
    console.error('❌ 高级示例执行失败:', error);
  }
}

async function troubleshootingExample() {
  console.log('\n=== 故障排除示例 ===\n');
  
  try {
    await myAuthMgr.init();
    
    // 1. 测试极端的时间容错设置
    console.log('🧪 测试不同的时间容错设置:');
    
    const testTolerances = [0, 60, 300, 600, 1800];
    
    for (const tolerance of testTolerances) {
      myAuthMgr.setTimeTolerance(tolerance);
      console.log(`  设置容错 ${tolerance}秒 (${Math.floor(tolerance/60)}分钟) - ✅`);
    }
    
    // 2. 测试无效的容错设置
    console.log('\n🚫 测试无效设置:');
    myAuthMgr.setTimeTolerance(-100); // 应该被拒绝
    
    // 3. 恢复合理的设置
    console.log('\n🔧 恢复推荐设置:');
    myAuthMgr.setTimeTolerance(300); // 5分钟
    console.log('  已恢复为推荐的5分钟容错设置');
    
  } catch (error) {
    console.error('❌ 故障排除示例执行失败:', error);
  }
}

// 运行所有示例
async function runAllExamples() {
  console.log('🚀 开始运行时间处理优化功能示例\n');
  
  await basicExample();
  await advancedExample();
  await troubleshootingExample();
  
  console.log('\n🎉 所有示例运行完成！');
  console.log('\n💡 提示：');
  console.log('  - 在生产环境中，建议使用较小的时间容错值');
  console.log('  - 根据网络环境选择合适的时间服务器');
  console.log('  - 定期检查和更新时间服务器列表');
}

// 如果直接运行此文件
if (require.main === module) {
  runAllExamples().catch(error => {
    console.error('❌ 示例运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  basicExample,
  advancedExample,
  troubleshootingExample,
  runAllExamples
};
