/**
 * 快速验证测试 - 验证时间处理优化功能
 */

const { myAuthMgr } = require('./license');

async function quickTest() {
  console.log('🚀 开始快速验证测试\n');
  
  try {
    // 1. 初始化
    console.log('1️⃣ 初始化授权管理器...');
    await myAuthMgr.init();
    console.log('✅ 初始化完成\n');
    
    // 2. 测试配置API
    console.log('2️⃣ 测试配置API...');
    
    // 查看默认配置
    console.log('📋 默认配置:');
    console.log(`   时间容错: ${myAuthMgr.getTimeTolerance()}秒`);
    console.log(`   时间服务器数量: ${myAuthMgr.getTimeServers().length}`);
    
    // 修改配置
    myAuthMgr.setTimeTolerance(600);
    myAuthMgr.addTimeServer('https://www.google.com/', 3000, 'Google测试服务器');
    
    console.log('📋 更新后配置:');
    console.log(`   时间容错: ${myAuthMgr.getTimeTolerance()}秒`);
    console.log(`   时间服务器数量: ${myAuthMgr.getTimeServers().length}`);
    console.log('✅ 配置API测试通过\n');
    
    // 3. 测试机器码获取
    console.log('3️⃣ 测试机器码获取...');
    const machineCode = await myAuthMgr.getMachineCode();
    console.log(`🔑 机器码: ${machineCode}`);
    console.log('✅ 机器码获取成功\n');
    
    // 4. 测试时间获取（通过授权检查触发）
    console.log('4️⃣ 测试时间获取机制...');
    console.log('📡 开始获取网络时间（可能需要几秒钟）...');
    
    const startTime = Date.now();
    const result = await myAuthMgr.checkAuth('QuickTest');
    const endTime = Date.now();
    
    console.log(`⏱️ 时间获取耗时: ${endTime - startTime}ms`);
    console.log(`📊 授权检查结果: 错误码 ${result.errCode}`);
    console.log('✅ 时间获取机制测试完成\n');
    
    // 5. 显示服务器列表
    console.log('5️⃣ 当前时间服务器配置:');
    const servers = myAuthMgr.getTimeServers();
    servers.forEach((server, index) => {
      console.log(`   ${index + 1}. ${server.description}`);
      console.log(`      URL: ${server.url}`);
      console.log(`      超时: ${server.timeout}ms`);
    });
    
    console.log('\n🎉 快速验证测试完成！');
    console.log('\n💡 测试结果说明:');
    console.log('   - 如果看到多个时间服务器尝试日志，说明多服务器功能正常');
    console.log('   - 如果看到重试日志，说明智能重试机制正常');
    console.log('   - 如果看到时间容错相关日志，说明容错机制正常');
    console.log('   - 配置API功能已验证正常工作');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.log('\n🔧 可能的原因:');
    console.log('   - 网络连接问题');
    console.log('   - 依赖包未正确安装');
    console.log('   - 代码编译问题');
  }
}

// 运行测试
if (require.main === module) {
  quickTest().then(() => {
    console.log('\n✨ 测试脚本执行完成');
  }).catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { quickTest };
