const { myAuthMgr } = require('./license');

async function testTimeHandling() {
  console.log('=== 测试时间处理改进 ===\n');
  
  try {
    // 初始化授权管理器
    await myAuthMgr.init();
    
    // 获取机器码
    const machineCode = await myAuthMgr.getMachineCode();
    console.log('机器码:', machineCode);
    
    // 测试授权检查
    console.log('\n--- 测试授权检查 ---');
    const result = await myAuthMgr.checkAuth('TestSoftware');
    
    console.log('授权检查结果:');
    console.log('错误码:', result.errCode);
    console.log('错误信息:', result.errInfo);
    
    if (result.permissionId) {
      console.log('权限ID:', result.permissionId);
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
testTimeHandling().then(() => {
  console.log('\n测试完成');
}).catch(error => {
  console.error('测试失败:', error);
});
