const { myAuthMgr } = require('./license');

async function testTimeHandling() {
  console.log('=== 测试时间处理改进功能 ===\n');

  try {
    // 初始化授权管理器
    await myAuthMgr.init();

    // 获取机器码
    const machineCode = await myAuthMgr.getMachineCode();
    console.log('🔑 机器码:', machineCode);

    // 测试配置功能
    console.log('\n--- 测试配置功能 ---');

    // 显示当前时间容错设置
    console.log('⏰ 当前时间容错设置:', myAuthMgr.getTimeTolerance(), '秒');

    // 显示当前时间服务器列表
    console.log('🌐 当前时间服务器列表:');
    const servers = myAuthMgr.getTimeServers();
    servers.forEach((server, index) => {
      console.log(`  ${index + 1}. ${server.description}: ${server.url} (超时: ${server.timeout}ms)`);
    });

    // 测试设置时间容错
    console.log('\n--- 测试时间容错设置 ---');
    myAuthMgr.setTimeTolerance(600); // 设置为10分钟
    console.log('✅ 时间容错已设置为:', myAuthMgr.getTimeTolerance(), '秒');

    // 测试添加自定义时间服务器
    console.log('\n--- 测试添加自定义时间服务器 ---');
    myAuthMgr.addTimeServer('https://www.google.com/', 3000, 'Google服务器');

    console.log('🌐 更新后的时间服务器列表:');
    const updatedServers = myAuthMgr.getTimeServers();
    updatedServers.forEach((server, index) => {
      console.log(`  ${index + 1}. ${server.description}: ${server.url} (超时: ${server.timeout}ms)`);
    });

    // 测试时间获取（这会触发多服务器重试机制）
    console.log('\n--- 测试多服务器时间获取 ---');
    console.log('🕐 开始获取网络时间...');

    // 这里我们直接调用内部方法来测试（仅用于演示）
    // 在实际使用中，时间获取会在checkAuth或activate时自动进行

    // 测试授权检查
    console.log('\n--- 测试授权检查 ---');
    const result = await myAuthMgr.checkAuth('TestSoftware');

    console.log('📋 授权检查结果:');
    console.log('  错误码:', result.errCode);
    console.log('  错误信息:', result.errInfo);

    if (result.permissionId) {
      console.log('  权限ID:', result.permissionId);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

async function testTimeToleranceDemo() {
  console.log('\n=== 时间容错机制演示 ===\n');

  try {
    // 演示不同的时间容错设置
    const toleranceValues = [0, 300, 600, 1800]; // 0秒、5分钟、10分钟、30分钟

    for (const tolerance of toleranceValues) {
      console.log(`\n--- 测试时间容错: ${tolerance}秒 (${Math.floor(tolerance/60)}分钟) ---`);
      myAuthMgr.setTimeTolerance(tolerance);

      // 这里可以添加具体的授权测试
      // 由于没有实际的授权数据，我们只演示配置
      console.log(`✅ 时间容错已设置为 ${tolerance}秒`);
    }

  } catch (error) {
    console.error('❌ 时间容错演示失败:', error);
  }
}

// 运行所有测试
async function runAllTests() {
  await testTimeHandling();
  await testTimeToleranceDemo();
  console.log('\n🎉 所有测试完成');
}

runAllTests().catch(error => {
  console.error('❌ 测试失败:', error);
});
